// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Util from "../Base/Util";
import GameManager from "../GameManager";
import GameUtils, { ObjectPool, UnitInfo } from "./GameUtils";
import LocalUtils from "../LocalUtils";
import Enemy from "../GameStuffAndComps/Enemy";
import GameWorld from "./GameWorld";
import NPC from "../GameStuffAndComps/NPC";
import GameStuffManager from "./GameStuffManager";
import GameDirector from "./GameDirector";

const {ccclass, property} = cc._decorator;

@ccclass
export default class NPCCreator extends cc.Component {


    @property(GameWorld)
    targetGameWorld: GameWorld = null;

    @property(cc.Prefab)
    npcSoldierPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    npcLoggerPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    npcBuyerPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    npcWorkerPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    npcSwordmanPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    npcMeatCollectorPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    npcArcherPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    npcDiggerPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    npcSlingerPrefab: cc.Prefab = null;

    // @property([cc.Node])
    // NPCBuyerPathPoints: cc.Node[] = [];
    // @property([cc.Node])
    // NPCBuyerLeftPathPoints: cc.Node[] = [];

    // @property(cc.Prefab)
    // NPCHunterPrefab: cc.Prefab = null;
    // @property([cc.Node])
    // NPCHunterPathPoints: cc.Node[] = [];

    // @property(cc.Prefab)
    // NPCWorkerPrefab: cc.Prefab = null;
    // @property([cc.Node])
    // NPCWorkerPathPoints: cc.Node[] = [];

    npcBuyerNumUpLimit = 1;
    npcBuyerListNumUpLimit = 20;
    npcHunterNumUpLimit = 5;

    createCDTime = 0.1;
    createWaveTime = 0.1;

    isNPCHunterMoving = false;

    isAllowCreateLeftBuyer = false;

    // NPCPools: ObjectPool<UnitInfo>[] = [];
    // NPCNodePools: cc.NodePool[] = [];

    private _isAllowCreate = true;
    private _createTime = 0;
    private _creatingTime = 0;
    private _isCreating = false;
    private _isCreated = false;

    private _isAllowCreateInArea1 = true;
    private _isAllowCreateInArea2 = false;
    
    private _isFirstLoggerCreated = false;
    private _isFirstBuyerCreated = false;

    private _movingNPCHunterNum = 0;
    
    // LIFE-CYCLE CALLBACKS:
    onLoad () {
        GameUtils.rootNPCCreator = this;
        // this.InitNPCPools();
    }
    start () {
        GameManager.instance.AddGameUpdate('NpcCreator', (dt: number)=>{
            this.gameUpdate(dt);
        });
    }

    StartCreate() {
        this._creatingTime = this.createWaveTime;
        // this._isAllowCreate = true;
        // GameManager.instance.LateFrameCall(()=>{
        //     this.CreateFirstWave();
        // });
    }

    // InitNPCPools() {
    //     for(let i = 0; i < 2; i++) {
    //         this.NPCPools[i] = new ObjectPool<UnitInfo>();
    //         this.NPCNodePools[i] = new cc.NodePool();
    //         this.NPCPools[i].SetOnPutCallback((info: UnitInfo)=>{
    //             info.node.active = false;
    //             this.NPCNodePools[i].put(info.node);
    //         });
    //         this.NPCPools[i].SetOnTakeCallback((info: UnitInfo)=>{
    //             info.node.active = true;
    //             let node = this.NPCNodePools[i].get();
    //             node.parent = this.targetGameWorld.MiddleNode;
    //             info.node = node;
    //         });
    //     }
    // }

    gameUpdate(dt: number) {
        if(this._isAllowCreate) {
            this._createTime += dt;
            if(!this._isCreated && !this._isCreating && this._createTime > this.createCDTime) {
                this.CheckCreateWave();
            }
            if(this._isCreating) {
                this._creatingTime += dt;
                if(this._creatingTime > this.createWaveTime) {
                    this.CreateWave();
                    this._creatingTime = this._creatingTime - this.createWaveTime;
                    this._isCreating = false;
                    this._isCreated = false;
                }
            }
        }
    }

    CheckCreateWave() {
        // if(GameStuffManager.instance.npcSoldierList.length < 4) {
        if(!GameStuffManager.instance.inQueueUnitList[1] || GameStuffManager.instance.inQueueUnitList[1].length < this.npcBuyerListNumUpLimit) {
            this._createTime = this._createTime - this.createCDTime;
            this._isCreating = true;
        }
    }

    CreateWave() {
        // this.CreateANPCSoldier();
        this.CreateANPCBuyer();
    }

    CreateFirstWave() {
        let npcs: NPC[] = [];
        for(let i = 0; i < 5; i++) {
            let npcInfo = this.CreateANPCBuyer();
            if(npcInfo) {
                npcs.push(npcInfo.script as NPC);
            }
        }
        npcs.forEach((e, index)=>{
            e.isFirstWaveBuyer = true;
            let pos1 = e.GetPathPointPosition(1);
            let pos2 = e.GetPathPointPosition(2);
            e.firstWaveBuyerTPPosition = pos2.lerp(pos1, index / 5);
        });
    }

    CreateANPCLogger(pos?: cc.Vec2): UnitInfo {
        if(!pos) {
            // pos = cc.v2(200, -500);
            pos = GameUtils.rootGameWorld.floorUnlockAreaList[1].rootPosition;
            // pos = GameUtils.rootGuideToStatue.statues[1].rootPosition.add(cc.v2(-100, -100));
            if(GameUtils.rootGameWorld.statues[1]) {
                pos = GameUtils.rootGameWorld.statues[1].rootPosition.add(cc.v2(0, -80));
            }
        }
        let prefab = this.npcLoggerPrefab;
        if(!prefab) { return; }
        let npcInfo = this.GenerateANPC(pos, prefab);
        // (NPCInfo.script as NPC).;
        GameStuffManager.instance.PushNPCInfo(npcInfo, 3);
        GameUtils.rootGameWorldUI.GenerateNewNPCHPBar(npcInfo.script as NPC);
        GameUtils.rootGameWorldUI.GenerateNpcDemandBubble(npcInfo.script as NPC);
        GameManager.instance.LateFrameCall(()=>{
            LocalUtils.PlaySound('level_up');
            GameUtils.rootGameWorldUI.PlayLevelUp(false, npcInfo.script);
        });
        if(!this._isFirstLoggerCreated) {
            this._isFirstLoggerCreated = true;
        }
        console.log(`生成 NPC 伐木工！`);
        return npcInfo;
    }

    CreateANPCMeatCollector(pos?: cc.Vec2): UnitInfo {
        if(!pos) {
            pos = GameUtils.rootGameWorld.floorUnlockAreaList[0].rootPosition;
        }
        let prefab = this.npcMeatCollectorPrefab;
        if(!prefab) { return; }
        let npcInfo = this.GenerateANPC(pos, prefab);
        GameStuffManager.instance.PushNPCInfo(npcInfo, 7);
        GameUtils.rootGameWorldUI.GenerateNewNPCHPBar(npcInfo.script as NPC);
        GameUtils.rootGameWorldUI.GenerateNpcDemandBubble(npcInfo.script as NPC);
        GameManager.instance.LateFrameCall(()=>{
            LocalUtils.PlaySound('level_up');
            GameUtils.rootGameWorldUI.PlayLevelUp(false, npcInfo.script);
        });
        console.log(`生成 NPC 捡肉农民！`);
        return npcInfo;
    }

    CreateANPCArcher(pos?: cc.Vec2) {
        if(!pos) {
            pos = GameUtils.rootGameWorld.floorUnlockAreaList[11].rootPosition;
        }
        let prefab = this.npcArcherPrefab;
        if(!prefab) { return; }
        let npcInfo = this.GenerateANPC(pos, prefab);
        GameStuffManager.instance.PushNPCInfo(npcInfo, 8);
        GameUtils.rootGameWorldUI.GenerateNewNPCHPBar(npcInfo.script as NPC);
        GameUtils.rootGameWorldUI.GenerateNpcDemandBubble(npcInfo.script as NPC);
        GameUtils.rootGameWorldUI.GenerateArrowBubble(npcInfo.script as NPC);
        GameManager.instance.LateFrameCall(()=>{
            LocalUtils.PlaySound('level_up');
            GameUtils.rootGameWorldUI.PlayLevelUp(false, npcInfo.script);
        });
        console.log(`生成 NPC 弓箭手！`);
        return npcInfo;
    }

    CreateANPCSoldier(pos?: cc.Vec2) {
        if(!pos) {
            pos = cc.v2(GameUtils.rootPathPoints.npcPathPoints[0].position);
        }
        let prefab = this.npcSoldierPrefab;
        let npcInfo = this.GenerateANPC(pos, prefab);
        (npcInfo.script as NPC).isSoldier = true;
        GameStuffManager.instance.PushNPCInfo(npcInfo, 1);
        GameUtils.rootGameWorldUI.GenerateNewNPCHPBar(npcInfo.script as NPC);
        // GameManager.instance.LateFrameCall(()=>{
        //     LocalUtils.PlaySound('level_up');
        //     GameUtils.rootGameWorldUI.PlayLevelUp(false, npcInfo.script);
        // });
        // console.log(`生成 NPC 士兵！now: ${GameStuffManager.instance.npcList.length}`);
    }

    CreateANPCSwordman(pos?: cc.Vec2) {
        if(!pos) {
            pos = cc.v2(GameUtils.rootPathPoints.npcPathPoints[1].position);
        }
        let prefab = this.npcSwordmanPrefab;
        let npcInfo = this.GenerateANPC(pos, prefab);
        (npcInfo.script as NPC).isSoldier = true;
        GameStuffManager.instance.PushNPCInfo(npcInfo, 6);
        GameUtils.rootGameWorldUI.GenerateNewNPCHPBar(npcInfo.script as NPC);
        GameManager.instance.LateFrameCall(()=>{
            LocalUtils.PlaySound('level_up');
            GameUtils.rootGameWorldUI.PlayLevelUp(false, npcInfo.script);
        });
        // console.log(`生成 NPC 剑士！`);
    }

    CreateANPCBuyer(pos?: cc.Vec2) {
        if(!pos) {
            // pos = cc.v2(GameUtils.rootPathPoints.npcPathPoints[0].position);
            pos = cc.v2(GameUtils.rootPathPoints.GetTrackPathPoints(1)[0].position);
        }
        let buyerNum = GameUtils.npcBuyerList.length;
        if(buyerNum < this.npcBuyerNumUpLimit) {
            let prefab = this.npcBuyerPrefab;
            let npcInfo = this.GenerateANPC(pos, prefab);
            GameStuffManager.instance.PushNPCInfo(npcInfo, 4);
            GameManager.instance.LateFrameCall(()=>{
                LocalUtils.PlaySound('level_up');
                GameUtils.rootGameWorldUI.PlayLevelUp(false, npcInfo.script);
            });
            // console.log(`生成 NPC 买家！`);
            return npcInfo;
        }
    }

    // CreateANPCHunter(pos?: cc.Vec2) {
    //     if(!pos) {
    //         pos = this.NPCHunterPathPoints[0].getPosition();
    //     }
    //     let hunterNum = GameUtils.NPCHunterList.length;
    //     if(hunterNum < this.NPCHunterNumUpLimit) {
    //         let prefab = this.NPCHunterPrefab;
    //         let NPCInfo = this.GenerateANPC(pos, prefab);
    //         this.targetGameWorld.PushNPCInfo(NPCInfo, 2);
    //         GameManager.instance.LateFrameCall(()=>{
    //             LocalUtils.PlaySound('level_up');
    //             GameUtils.rootGameWorldUI.PlayLevelUp(false, NPCInfo.script);
    //         });
    //         // console.log(`生成 NPC 猎人！`);
    //         this._movingNPCHunterNum += 1;
    //         GameManager.instance.LateTimeCallOnce(()=>{
    //             this._movingNPCHunterNum -= 1;
    //         }, 2);
    //     } else {
    //         // console.log(`生成失败！hunterNum: ${hunterNum}, NPCHunterNumUpLimit: ${this.NPCHunterNumUpLimit}`);
    //     }
    // }

    CreateANPCWorker(pos?: cc.Vec2, isMaker = false) {
        if(!pos) {
            pos = GameUtils.rootPathPoints.npcWorkerPathPoints[0].getPosition();
        }
        let prefab = this.npcWorkerPrefab;
        let npcInfo = this.GenerateANPC(pos, prefab);
        GameManager.instance.LateFrameCall(()=>{
            LocalUtils.PlaySound('level_up');
            GameUtils.rootGameWorldUI.PlayLevelUp(false, npcInfo.script);
        });
        (npcInfo.script as NPC).isWorkerIsMaker = isMaker;
        GameStuffManager.instance.worker = npcInfo.script as NPC;
        console.log(`生成 NPC 工人！`);
        return npcInfo;
    }

    CreateANPCDigger(pos?: cc.Vec2) {
        if(!pos) {
            pos = cc.v2(GameUtils.rootPathPoints.npcPathPoints[0].position);
        }
        let prefab = this.npcDiggerPrefab;
        let npcInfo = this.GenerateANPC(pos, prefab);
        GameStuffManager.instance.PushNPCInfo(npcInfo, 9);
        // GameUtils.rootGameWorldUI.GenerateNewNPCHPBar(npcInfo.script as NPC);
        // GameUtils.rootGameWorldUI.GenerateNpcDemandBubble(npcInfo.script as NPC);
        GameManager.instance.LateFrameCall(()=>{
            LocalUtils.PlaySound('level_up');
            GameUtils.rootGameWorldUI.PlayLevelUp(false, npcInfo.script);
        });
        // GameDirector.instance.OnNpcDiggerCreated();
        console.log(`生成 NPC 挖掘者！`);
        return npcInfo;
    }

    CreateANPCSlinger(pos?: cc.Vec2) {
        if(!pos) {
            pos = cc.v2(GameUtils.rootPathPoints.npcPathPoints[1].position);
        }
        let prefab = this.npcSlingerPrefab;
        let npcInfo = this.GenerateANPC(pos, prefab);
        GameStuffManager.instance.PushNPCInfo(npcInfo, 10);
        // GameUtils.rootGameWorldUI.GenerateNewNPCHPBar(npcInfo.script as NPC);
        // GameUtils.rootGameWorldUI.GenerateNpcDemandBubble(npcInfo.script as NPC);
        GameManager.instance.LateFrameCall(()=>{
            LocalUtils.PlaySound('level_up');
            GameUtils.rootGameWorldUI.PlayLevelUp(false, npcInfo.script);
        });
        // GameDirector.instance.OnNpcDiggerCreated();
        console.log(`生成 NPC 投掷兵！`);
        return npcInfo;
    }

    GenerateANPC(pos: cc.Vec2, prefab: cc.Prefab) {
        let NPCInfo: UnitInfo = null;
        let NPCScript: NPC = null;
        // NPCInfo = this.TryGenerateFromNodePool(2);
        // if(NPCInfo == null) {
            let node = LocalUtils.GenerateNode(this.targetGameWorld.MiddleNode, prefab, cc.v3());
            NPCScript = node.getComponent(NPC);
            NPCInfo = new UnitInfo(node);
            NPCInfo.script = NPCScript;
            NPCScript.info = NPCInfo;
            NPCScript.OnBorn();
            GameManager.instance.LateFrameCall(()=>{
                if(NPCScript.npcRef == 4 && NPCScript.isFirstWaveBuyer) {
                    NPCScript.rootPosition = NPCScript.firstWaveBuyerTPPosition;
                } else {
                    NPCScript.rootPosition = pos;
                }
            });
        // } else {
        //     NPCScript = NPCInfo.script as NPC;
        //     NPCScript.Reset(pos);
        // }
        return NPCInfo;
    }

    // TryGenerateFromNodePool(enemyPrefabIndex: number): UnitInfo {
    //     let enemyInfo: UnitInfo = null;
    //     if (this.NPCPools[enemyPrefabIndex].Size() > 0) {
    //         enemyInfo = this.NPCPools[enemyPrefabIndex].Take();
    //         // console.log(`成功从对象池中取出对象！ id: ${enemyInfo.script.unit_id}`);
    //         return enemyInfo;
    //     } else {
    //         // console.log('对象池没有对象！');
    //         return null;
    //     }
    // }

    // TryRecoveryNPC(info: UnitInfo) {
    //     let index = (info.script as NPC).NPCRef - 1;
    //     if(index < 3) {
    //         this.NPCPools[index].Put(info);
    //         // console.log(`成功向对象池中加入对象！ id: ${info.script.unit_id}`);
    //         return true;
    //     }
    //     return false;
    // }
}
