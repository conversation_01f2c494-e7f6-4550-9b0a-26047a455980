// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import LocalUtils from "../../LocalUtils";
import Stuff from "../Stuff";

const {ccclass, property, executeInEditMode} = cc._decorator;

@ccclass
export default class MiddleNodeReleaser extends cc.Component {
    @property({type: cc.Node, tooltip: '根节点：此节点必须为 MiddleNode 节点的子节点，由此可以将目标节点释放在 MiddleNode 下'})
    rootNode: cc.Node = null;

    @property({tooltip: '自动模式：自动寻找子节点中的所有 Stuff 节点（如果是 Stuff 节点，不会再检测该 Stuff 的子节点）'})
    isAuto: boolean = true;

    @property({type: [cc.Node], visible: function() {
        return !this.isAuto;
    }})
    targetNodes: cc.Node[] = [];

    protected onLoad(): void {
        let count = 0;
        if(this.isAuto) {
            let nodes = this.FindStuffNodes(this.node, true);
            nodes.forEach((e)=>{
                LocalUtils.ChangeParentWithoutMoving(e, this.rootNode.parent);
                count ++;
            });
        } else {
            this.targetNodes.forEach((e)=>{
                LocalUtils.ChangeParentWithoutMoving(e, this.rootNode.parent);
                count ++;
            });
        }
        // console.log(`MiddleNodeReleaser: ${count} 个节点已释放！`);
    }

    FindStuffNodes(node: cc.Node, isFirst = false): cc.Node[] {
        let nodes: cc.Node[] = [];
        if(!node) return [];
        if(!isFirst && node.getComponent(MiddleNodeReleaser)) return [];
        if(node.getComponent(Stuff)) {
            nodes = nodes.concat([node]);
        } else {
            this.node.children.forEach((e)=>{
                nodes = nodes.concat(this.FindStuffNodes(e));
            });
        }
        return nodes;
    }
    
}