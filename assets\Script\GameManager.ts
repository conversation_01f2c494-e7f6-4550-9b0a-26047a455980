// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import LayerManger from "./Base/Components/LayerManger";
import GameUtils, { DataCountBucket, ObjectPool } from "./Game/GameUtils";
import LocalUtils from "./LocalUtils";

const {ccclass, property} = cc._decorator;

@ccclass
export default class GameManager extends cc.Component {

    public static get instance(): GameManager {
        if(!this._instance) {
            this._instance = cc.find('root').getComponent(GameManager);
        }
        return this._instance;
    }

    public get NumOfRunningCallback(): number {
        return this._updateCallbackList.length + this._gameUpdateList.length + this._lateUpdateCallbackList.length;
    }

    public get RunningCallbacks(): UpdateCallbackInfo[] {
        return this._updateCallbackList.concat(this._gameUpdateList).concat(this._lateUpdateCallbackList);
    }

    public get isPause(): boolean {
        return this._isPause;
    }

    public get date(): Date {
        return new Date();
    }

    runningTime = 0;

    private _recordFrameStartTime = 0;

    private static _instance: GameManager = null;
    
    private _callbacksCostTime: DataCountBucket = new DataCountBucket();
    private _countBucket: DataCountBucket = new DataCountBucket();
    
    private _updataCallbackObjectPool: ObjectPool<UpdateCallbackInfo> = new ObjectPool();

    private _updateCallbackList: UpdateCallbackInfo[] = [];
    private _gameUpdateList: UpdateCallbackInfo[] = [];
    private _lateUpdateCallbackList: UpdateCallbackInfo[] = [];
    private _lateTimeCallbackList: LateTimeCallCallbackInfo[] = [];

    private get _curID() {
        return this.__curID;
    }

    private set _curID(value: number) {
        this.__curID = value;
        if(value % 10000 == 0) {
            let max = Number.MAX_VALUE / Math.pow(10, 100);
            // console.warn(`update callback id is (10,000) x [${value / 10000}] now ! (to Max: ${value / max})`);
            console.warn(`update callback id is (10,000) x [${value / 10000}] now ! (to Max: ${value / max})`);
            // console.log(`mark count bucket: \n${this._countBucket.toString()}`);
        }
    }

    private __curID = 0;

    private _lateTimeCallbackId = -1;

    private _gameTimeScale = 1;

    private _leftCallbackList: UpdateCallbackInfo[] = [];
    private _curCallbackIndex = 0;
    private _curLevel = 0;
    private _curCallingCallbackInfo: UpdateCallbackInfo = null;


    private _isPause = false;
    private _isStep = false;
    private _time = 0;
    private _gameTime = 0;

    private _resizeFunc: any = null;

    private _lastTimeRecord = this.date.getTime();

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        console.log('🎮Game Load !!');
        cc.director.getPhysicsManager().enabled = true;
        cc.director.getPhysicsManager().gravity = cc.v2(0, 0);
        GameManager._instance = this;
        LocalUtils.rootGameManager = this;

        this._curID = 0;
        
        this._resizeFunc = () => {
            this.onResize();
        }
    }

    start () {
        this._recordFrameStartTime = this.date.getTime();
        console.log('🎮Start !!');
        GameUtils.GameStart();
        
        LayerManger.Instance.AddResizeCallback(this._resizeFunc);
        this.onResize();

        this._lateTimeCallbackId = this.AddGameUpdate('LateTimeCall', (dt: number)=>{
            this._lateTimeCallbackList = this._lateTimeCallbackList.filter((e)=>{
                return !e.isFinish;
            });
            this._lateTimeCallbackList.forEach((e)=>{
                if(!e.isFinish) {
                    e.time += dt;
                    if(e.time > e.callTime) {
                        e.call(dt);
                        e.time = 0;
                    }
                }
            });
        });
    }

    update (dt: number) {
        this.runningTime += this.date.getTime() - this._recordFrameStartTime;
        this._recordFrameStartTime = this.date.getTime();
        this._lastTimeRecord = this.date.getTime();
        this._time += dt;
        this._updateCallbackList.forEach((e)=>{
            if(e.isReadyToRemove) {
                this.PutCallbackInfoInPool(e);
            }
        });
        this._gameUpdateList.forEach((e)=>{
            if(e.isReadyToRemove) {
                this.PutCallbackInfoInPool(e);
            }
        });
        this._lateUpdateCallbackList.forEach((e)=>{
            if(e.isReadyToRemove) {
                this.PutCallbackInfoInPool(e);
            }
        });
        this._updateCallbackList = this._updateCallbackList.filter((e)=>{
            return !e.isReadyToRemove;
        });
        this._gameUpdateList = this._gameUpdateList.filter((e)=>{
            return !e.isReadyToRemove;
        });
        this._lateUpdateCallbackList = this._lateUpdateCallbackList.filter((e)=>{
            return !e.isReadyToRemove;
        });
        this._curLevel = 1;
        this.StartUpdateCallback(dt);
        this._curLevel = 2;
        this.StartGameUpdate(dt);
        this._curLevel = 3;

        // console.log('Game Manager Update Time Cost: ', timeCost);
        
    }

    lateUpdate (dt: number) {
        this._curLevel = 4;
        this.StartLateUpdateCallback(dt);
        this._curLevel = 5;

        let timeCost = 0;
        let time = 0;
        let timeCostIndex = 0;
        this._callbacksCostTime.clearAll();
        this._updateCallbackList.forEach((e)=>{
            this._callbacksCostTime.putIn('[' + e.mark + '], ' + e.id, e.lastCostTime);
            timeCost += e.lastCostTime;
            if(timeCost >= 1) {
                time = Math.floor(timeCost);
                this._callbacksCostTime.putIn('[Time Cost ' + timeCostIndex + ']', time);
                timeCost -= timeCost;
                timeCostIndex += 1;
            }
        });
        this._gameUpdateList.forEach((e)=>{
            this._callbacksCostTime.putIn('[' + e.mark + '], ' + e.id, e.lastCostTime);
            timeCost += e.lastCostTime;
            if(timeCost >= 1) {
                time = Math.floor(timeCost);
                this._callbacksCostTime.putIn('[Time Cost ' + timeCostIndex + ']', time);
                timeCost -= timeCost;
                timeCostIndex += 1;
            }
        });
        this._lateUpdateCallbackList.forEach((e)=>{
            this._callbacksCostTime.putIn('[' + e.mark + '], ' + e.id, e.lastCostTime);
            timeCost += e.lastCostTime;
            if(timeCost >= 1) {
                time = Math.floor(timeCost);
                this._callbacksCostTime.putIn('[Time Cost ' + timeCostIndex + ']', time);
                timeCost -= timeCost;
                timeCostIndex += 1;
            }
        });

        if(this._isStep) {
            this._isStep = false;
            this.GamePause();
        }
    }

    protected onDestroy() {
        LayerManger.Instance.RemoveResizeCallback(this._resizeFunc);
    }

    GamePause() {
        this._isPause = true;
        this._gameTimeScale = 0;
    }

    GameResume() {
        this._isPause = false;
        this._gameTimeScale = 1;
    }

    GameStep() {
        this._isPause = false;
        this._isStep = true;
        this._gameTimeScale = 1;
    }

    LogCostTime() {
        console.log(`callbacks cost time: \n${this._callbacksCostTime.toString()}`);
        console.log(`mark count bucket: \n${this._countBucket.toString()}`);
        this._countBucket.clearAll();
    }

    StartUpdateCallback(dt: number) {
        let lastCostTime = 0;
        let countIn1ms = 0;
        let callbackInfos: UpdateCallbackInfo[] = [];

        this.ReSortCallbackList(this._updateCallbackList);
        this._leftCallbackList = [];
        this._updateCallbackList.forEach((e, index)=>{
            e.isActive = true;
            this._leftCallbackList.push(e);
        });

        let loopTimes = 0;
        let curCallIndex = 0;
        while(this._leftCallbackList[curCallIndex] && loopTimes < 100000) {
            loopTimes += 1;
            
            let callbackInfo = this._leftCallbackList[curCallIndex];
            curCallIndex += 1;
            
            this._curCallingCallbackInfo = callbackInfo;
            // try {
                callbackInfo.call(dt);
            // } catch (error) {
            //     console.error(error);
            // }

            if(callbackInfo.callIndex != this._curCallbackIndex) {
                console.error('callbackInfo.callIndex != this._curCallbackIndex');
            }
            this._curCallbackIndex += 1;

            let timeRecord = this.date.getTime();
            lastCostTime = timeRecord - this._lastTimeRecord;
            if(lastCostTime == 0) {
                countIn1ms += 1;
                callbackInfos.push(callbackInfo);
            } else {
                let time = lastCostTime / countIn1ms;
                callbackInfos.forEach((e)=>{
                    e.lastCostTime = time;
                });
                callbackInfos = [];
                countIn1ms = 0;
                this._lastTimeRecord = timeRecord;
            }

            loopTimes += 1;
        }
        callbackInfos.forEach((e)=>{
            e.lastCostTime = 1 / countIn1ms;
        });
        callbackInfos = [];
        this._curCallbackIndex = 0;
    }

    StartGameUpdate(dt: number) {
        if(!this._isPause) {
            let gameUpdateStartTimeRecord = this.date.getTime();
            let lastCostTime = 0;
            let countIn1ms = 0;
            let callbackInfos: UpdateCallbackInfo[] = [];

            this._gameTime += dt * this._gameTimeScale;

            this.ReSortCallbackList(this._gameUpdateList);
            this._leftCallbackList = [];
            this._gameUpdateList.forEach((e, index)=>{
                e.isActive = true;
                this._leftCallbackList.push(e);
            });
    
            let loopTimes = 0;
            let curCallIndex = 0;
            while(this._leftCallbackList[curCallIndex] && loopTimes < 100000) {
                loopTimes += 1;
                
                let callbackInfo = this._leftCallbackList[curCallIndex];
                curCallIndex += 1;
                
                this._curCallingCallbackInfo = callbackInfo;
                // try {
                    callbackInfo.call(dt);
                // } catch (error) {
                //     console.error(error);
                // }
    
                if(callbackInfo.callIndex != this._curCallbackIndex) {
                    console.error('callbackInfo.callIndex != this._curCallbackIndex');
                }
                this._curCallbackIndex += 1;

                let timeRecord = this.date.getTime();
                lastCostTime = timeRecord - this._lastTimeRecord;
                if(lastCostTime == 0) {
                    countIn1ms += 1;
                    callbackInfos.push(callbackInfo);
                } else {
                    let time = lastCostTime / countIn1ms;
                    callbackInfos.forEach((e)=>{
                        e.lastCostTime = time;
                    });
                    callbackInfos = [];
                    countIn1ms = 0;
                    this._lastTimeRecord = timeRecord;
                }
            }
            callbackInfos.forEach((e)=>{
                e.lastCostTime = 1 / countIn1ms;
            });
            callbackInfos = [];
            this._curCallbackIndex = 0;
            let gameUpdateEndTimeRecord = this.date.getTime();
            // console.log(`costTime : ${gameUpdateEndTimeRecord - gameUpdateStartTimeRecord}`);
        }
    }

    StartLateUpdateCallback(dt: number) {
        let lastCostTime = 0;
        let countIn1ms = 0;
        let callbackInfos: UpdateCallbackInfo[] = [];

        this.ReSortCallbackList(this._lateUpdateCallbackList);
        this._leftCallbackList = [];
        this._lateUpdateCallbackList.forEach((e, index)=>{
            e.isActive = true;
            this._leftCallbackList.push(e);
        });

        let loopTimes = 0;
        let curCallIndex = 0;
        while(this._leftCallbackList[curCallIndex] && loopTimes < 100000) {
            loopTimes += 1;
            
            let callbackInfo = this._leftCallbackList[curCallIndex];
            curCallIndex += 1;
            
            this._curCallingCallbackInfo = callbackInfo;
            
            // try {
                callbackInfo.call(dt);
            // } catch (error) {
            //     console.error(error);
            // }

            if(callbackInfo.callIndex != this._curCallbackIndex) {
                console.error('callbackInfo.callIndex != this._curCallbackIndex');
            }
            this._curCallbackIndex += 1;

            let timeRecord = this.date.getTime();
            lastCostTime = timeRecord - this._lastTimeRecord;
            if(lastCostTime == 0) {
                countIn1ms += 1;
                callbackInfos.push(callbackInfo);
            } else {
                let time = lastCostTime / countIn1ms;
                callbackInfos.forEach((e)=>{
                    e.lastCostTime = time;
                });
                callbackInfos = [];
                countIn1ms = 0;
                this._lastTimeRecord = timeRecord;
            }
        }
        callbackInfos.forEach((e)=>{
            e.lastCostTime = 1 / countIn1ms;
        });
        callbackInfos = [];
        this._curCallbackIndex = 0;
    }

    AddUpdateCallback(mark: string, callback: Function, isImmediatelyCall?: boolean, priority = 3000) {
        this._curID += 1;
        let callbackInfo = this.TryTakeCallbackInfoFromPool();
        if(callbackInfo) {
            callbackInfo.callback = callback;
            callbackInfo.priority = priority;
            callbackInfo.isReadyToRemove = false;
        } else {
            callbackInfo = new UpdateCallbackInfo(callback, priority);
        }
        callbackInfo.id = this._curID;
        callbackInfo.mark = mark;
        this._countBucket.putIn(mark);
        callbackInfo.isActive = isImmediatelyCall;
        callbackInfo.level = 0;

        this._updateCallbackList.push(callbackInfo);

        if(isImmediatelyCall && this._curLevel == 1 && priority >= this._curCallingCallbackInfo.priority) {
            this._leftCallbackList.push(callbackInfo);
            this.ReSortCallbackList(this._leftCallbackList);
        }

        return this._curID;
    }

    AddGameUpdate(mark: string, callback: Function, isImmediatelyCall?: boolean, priority = 3000) {
        this._curID += 1;
        let callbackInfo = this.TryTakeCallbackInfoFromPool();
        if(callbackInfo) {
            callbackInfo.callback = callback;
            callbackInfo.priority = priority;
            callbackInfo.isReadyToRemove = false;
        } else {
            callbackInfo = new UpdateCallbackInfo(callback, priority);
        }
        callbackInfo.id = this._curID;
        callbackInfo.mark = mark;
        this._countBucket.putIn(mark);
        callbackInfo.isActive = isImmediatelyCall;
        callbackInfo.level = 1;

        this._gameUpdateList.push(callbackInfo);

        if(isImmediatelyCall && this._curLevel == 2 && priority >= this._curCallingCallbackInfo.priority) {
            this._leftCallbackList.push(callbackInfo);
            this.ReSortCallbackList(this._leftCallbackList);
        }

        return this._curID;
    }

    AddLateUpdateCallback(mark: string, callback: Function, isImmediatelyCall?: boolean, priority = 3000) {
        this._curID += 1;
        let callbackInfo = this.TryTakeCallbackInfoFromPool();
        if(callbackInfo) {
            callbackInfo.callback = callback;
            callbackInfo.priority = priority;
            callbackInfo.isReadyToRemove = false;
        } else {
            callbackInfo = new UpdateCallbackInfo(callback, priority);
        }
        callbackInfo.id = this._curID;
        callbackInfo.mark = mark;
        this._countBucket.putIn(mark);
        callbackInfo.isActive = isImmediatelyCall;
        callbackInfo.level = 2;

        this._lateUpdateCallbackList.push(callbackInfo);

        if(isImmediatelyCall && this._curLevel == 4 && priority >= this._curCallingCallbackInfo.priority) {
            this._leftCallbackList.push(callbackInfo);
            this.ReSortCallbackList(this._leftCallbackList);
        }

        return this._curID;
    }

    RemoveUpdateCallback() {
        if(this._curLevel != 5) {
            if(this._curCallingCallbackInfo.id == this._lateTimeCallbackId) {
                console.warn(`Remove Fail! Callback is Late Time Callback! ID: ${this._lateTimeCallbackId}`);
                return;
            }
            let callbackInfo = this._curCallingCallbackInfo;
            callbackInfo.isReadyToRemove = true;
        }
    }
 
    RemoveUpdateCallbackByID(id: number) {
        if(id == this._lateTimeCallbackId) {
            console.warn(`Remove Fail! Callback is Late Time Callback! ID: ${id}`);
            return;
        }
        let callbackInfo = this.FindCallbackByID(id);
        if(callbackInfo) {
            callbackInfo.isReadyToRemove = true;
        } else {
            console.warn(`Remove Fail! CallbackID is Not exist! ID: ${id}`);
        }
    }

    ReSortCallbackList(list: UpdateCallbackInfo[]) {
        list.sort((a, b)=>{
            return a.priority - b.priority;
        });
        list.forEach((e, index)=>{
            e.callIndex = this._curCallbackIndex + index;
        });
    }

    CallOnce(callback: Function, isImmediatelyCall?: boolean, priority = 3000) {
        let nowRunningPriority = this._curCallingCallbackInfo.priority;
        this.AddGameUpdate('# CallOnce', (dt: number)=>{
            callback();
            this.RemoveUpdateCallback();
        }, true);
    }

    LateFrameCall(callback: Function) {
        this.AddGameUpdate('# LateFrameCall', (dt: number)=>{
            callback();
            this.RemoveUpdateCallback();
        });
    }

    LateTimeCallOnce(callback: Function, delay = 0) {
        // let time = 0;
        // this.AddGameUpdate('# LateTimeCallOnce', (dt: number)=>{
        //     time += dt;
        //     if(time > delay) {
        //         callback();
        //         this.RemoveUpdateCallback();
        //     }
        // });
        let callbackInfo = new LateTimeCallCallbackInfo(callback);
        callbackInfo.callLeftTimes = 1;
        callbackInfo.callTime = delay;
        this._lateTimeCallbackList.push(callbackInfo);
    }

    LateTimeCall(callback: Function, delay = 0) {
        // let time = 0;
        // this.AddGameUpdate('# LateTimeCall', (dt: number)=>{
        //     time += dt;
        //     if(time > delay) {
        //         callback();
        //         time -= delay;
        //     }
        // });
        let callbackInfo = new LateTimeCallCallbackInfo(callback);
        callbackInfo.callLeftTimes = -1;
        callbackInfo.callTime = delay;
        this._lateTimeCallbackList.push(callbackInfo);
    }

    IsCallbackExist(id: number) {
        let index = -1;
        let callbackInfo = this.FindCallbackByID(id);
        return callbackInfo && !callbackInfo.isReadyToRemove;
    }

    FindCallbackByID(id: number) {
        if(id < 0) {
            return null;
        }
        let findRef = 0;
        let callbackInfo = this._updateCallbackList.find((e)=>{
            return e.id == id;
        });
        if(!callbackInfo) {
            findRef = 1;
            callbackInfo = this._gameUpdateList.find((e)=>{
                return e.id == id;
            });
        }
        if(!callbackInfo) {
            findRef = 2;
            callbackInfo = this._lateUpdateCallbackList.find((e)=>{
                return e.id == id;
            });
        }
        if(callbackInfo) {
            return callbackInfo;
        } else {
            return null;
        }
    }

    protected PutCallbackInfoInPool(callbackInfo: UpdateCallbackInfo) {
        this._updataCallbackObjectPool.Put(callbackInfo);
    }

    protected TryTakeCallbackInfoFromPool() {
        let callbackInfo: UpdateCallbackInfo = null;
        if(this._updataCallbackObjectPool.Size() > 0) {
            callbackInfo = this._updataCallbackObjectPool.Take();
        }
        return callbackInfo;
    }

    protected onResize() {
        this.LateFrameCall(()=>{
            GameUtils.RefreshGameWorldRectSize();
            GameUtils.rootCameraControl.HardResetCameraPosition();
        });
    }
}

export class UpdateCallbackInfo {
    id: number;
    isActive = false;
    level: number;
    priority: number; // 优先级
    callIndex: number;
    mark: string;
    callback: Function;
    isReadyToRemove = false;

    lastCostTime = 0;

    constructor(callback: Function, priority: number) {
        this.callback = callback;
        this.priority = priority;
    }

    call(dt: number) {
        // console.log(`call ! ${this.isActive} dt: ${dt}`);
        
        if(this.isActive) {
            this.callback(dt);
        }
    }
}

export class LateTimeCallCallbackInfo {
    id: number;
    callback: Function;
    onFinishCallback: Function;
    time: number = 0;
    callTime = 0;
    callLeftTimes = -1;
    isFinish = false;

    constructor(callback: Function) {
        this.callback = callback;
    }

    call(dt: number) {
        // console.log(`call ! ${this.isActive} dt: ${dt}`);
        if(this.isFinish) {
            console.error('Call Failed !  This LateTimeCallCallbackInfo is finished !');
            return;
        }
        this.callback(dt);
        if(this.callLeftTimes > 0) {
            this.callLeftTimes -= 1;
            if(this.callLeftTimes <= 0) {
                this.Finish();
            }
        }
    }

    Finish() {
        if(this.onFinishCallback) {
            this.onFinishCallback();
        }
        this.isFinish = true;
    }
}