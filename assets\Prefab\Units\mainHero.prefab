[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "mainHero", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 7}, {"__id__": 40}, {"__id__": 31}, {"__id__": 65}], "_active": true, "_components": [], "_prefab": {"__id__": 69}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": ""}, {"__type__": "cc.Node", "_name": "rb", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": ""}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_type": 2, "_allowSleep": false, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": true, "enabledContactListener": false, "bullet": true, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsBoxCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "tag": 0, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 90, "height": 68}, "_id": ""}, {"__type__": "cc.PhysicsBoxCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "tag": 1, "_density": 1, "_sensor": true, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 50}, "_size": {"__type__": "cc.Size", "width": 60, "height": 120}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "399CL9eVhDkpr8ka0ExCz4", "sync": false}, {"__type__": "cc.Node", "_name": "backpack", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 8}, {"__id__": 14}, {"__id__": 23}], "_active": true, "_components": [], "_prefab": {"__id__": 39}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "storageArea", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 9}], "_active": true, "_components": [{"__id__": 12}], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "entityNode", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 120, "_color": {"__type__": "cc.Color", "r": 255, "g": 230, "b": 7, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "entityNode<Sprite>", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "e2gbVk8w9MDJ4rhkOUZrJ9", "sync": false}, {"__type__": "2486fvgJnNOO7C4RZrn1UnH", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "tag": "storageArea", "model_id": 0, "storageArea_id": 1, "resourceIds": [1, 2, 3, 4, 5, 6, 7, 10, 11, 12], "isBackpack": true, "isCanPutIn": false, "isCanTakeOut": false, "isFloorUI": false, "isWorkbench": false, "resourceGroups": [{"__id__": 13}, {"__id__": 22}, {"__id__": 29}], "workbenchInputStorgeArea": null, "workbenchOutputStorgeArea": null, "floorUI_id": 0, "floorUI_uiNode": null, "unlockDistance": 300, "_id": ""}, {"__type__": "8597cI3sDhE67Yr8Nmya5/Y", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "tag": "resourceGroup", "model_id": 0, "resourceGroup_id": 101, "resourceIds": [3, 2, 7], "_id": ""}, {"__type__": "cc.Node", "_name": "resourceGroup", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 15}, {"__id__": 18}], "_active": true, "_components": [{"__id__": 13}], "_prefab": {"__id__": 21}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "entityNode", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "entityNode<Sprite>", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "dbFfTxSRtDNoQj0hNGjJHD", "sync": false}, {"__type__": "cc.Node", "_name": "resources", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 10}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "resources<Sprite>", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "43GnHPPnNPvpcQYWOtvkmI", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "056mD8xMxJ3b2tliqRlThl", "sync": false}, {"__type__": "8597cI3sDhE67Yr8Nmya5/Y", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "tag": "resourceGroup", "model_id": 0, "resourceGroup_id": 102, "resourceIds": [1, 4, 5, 6], "_id": ""}, {"__type__": "cc.Node", "_name": "resourceGroup", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 24}, {"__id__": 26}], "_active": true, "_components": [{"__id__": 22}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "entityNode", "_objFlags": 0, "_parent": {"__id__": 23}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "de4lbAUbdF0JiQTdXZvFsM", "sync": false}, {"__type__": "cc.Node", "_name": "resources", "_objFlags": 0, "_parent": {"__id__": 23}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 27}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "74M92KDGFI8qRVy0L7K1//", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "8c1LS2J8FJ1Jn/0WpuWNGV", "sync": false}, {"__type__": "8597cI3sDhE67Yr8Nmya5/Y", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "tag": "resourceGroup", "model_id": 0, "resourceGroup_id": 103, "resourceIds": [10, 11, 12], "_id": ""}, {"__type__": "cc.Node", "_name": "resourceGroup", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 33}, {"__id__": 35}], "_active": true, "_components": [{"__id__": 29}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "backpackFrontNode", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 30}], "_active": true, "_components": [], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "3cPhiIwFtJbZUz7BZ4eiVf", "sync": false}, {"__type__": "cc.Node", "_name": "entityNode", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 34}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "2cp1aeCvVNNK8cH5cuBVLR", "sync": false}, {"__type__": "cc.Node", "_name": "resources", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 36}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "918UbpnU5H3aKcPeMAFw6J", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "71j6KQKI5M+6F9MDcAygTM", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "54ce+On31PkaIQvpNJUlKh", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "fccADY9AxEopMZ+6Nbryeq", "sync": false}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 41}, {"__id__": 44}, {"__id__": 47}], "_active": true, "_components": [{"__id__": 63}], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "guideCircle", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [], "_active": true, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 160, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 158, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "guideCircle<Sprite>", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c2846930-e8a2-4ff7-b1ff-c52f2b9c6c13"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "b7wCHI5nVDpo6mNKLQk4Cq", "sync": false}, {"__type__": "cc.Node", "_name": "shadow", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [], "_active": true, "_components": [{"__id__": 45}], "_prefab": {"__id__": 46}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1.5]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "shadow<Sprite>", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "517d57b4-7d44-43fd-875a-35fbfbe69712"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "36VPRkvudJ84HHYMBI62S3", "sync": false}, {"__type__": "cc.Node", "_name": "body", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [{"__id__": 48}, {"__id__": 56}, {"__id__": 59}], "_active": true, "_components": [], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "skill_light_parent", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [{"__id__": 49}], "_active": true, "_components": [], "_prefab": {"__id__": 55}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "skill_light", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [{"__id__": 50}], "_active": false, "_components": [{"__id__": 53}], "_prefab": {"__id__": 54}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-42.681, 31.567, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "da<PERSON><PERSON>_guang", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [], "_active": true, "_components": [{"__id__": 51}], "_prefab": {"__id__": 52}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 160.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10.799999999999997, 23, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "<PERSON><PERSON><PERSON>_guang<Sprite>", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8f40e7ef-adc4-4bf8-a50d-b9da2cd01e58"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "70bxwx6jZIRrsDSez7RXmD", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_defaultClip": {"__uuid__": "cfae02cb-51b3-4f71-b9cd-6d07f36766b1"}, "_clips": [{"__uuid__": "cfae02cb-51b3-4f71-b9cd-6d07f36766b1"}], "playOnLoad": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "b8yKgFChBBKKS++beuWS2n", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "72RG+zkp5BDZdNNzr6mH6P", "sync": false}, {"__type__": "cc.Node", "_name": "light", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [], "_active": false, "_components": [{"__id__": 57}], "_prefab": {"__id__": 58}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 164.7, "height": 160.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-31.328, 55.178, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "light<Sprite>", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8f40e7ef-adc4-4bf8-a50d-b9da2cd01e58"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "e86HpFlstBgZhmnndlTW/n", "sync": false}, {"__type__": "cc.Node", "_name": "spine", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 60}], "_prefab": {"__id__": 61}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 348.77, "height": 293.35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "idle", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "idle", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "48a16630-9550-4eab-a5dd-8ee506f5c2d4"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "03lnYC7FlIn79YgbPeLRvd", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "bbWATJPupGmqa+oKTk2y27", "sync": false}, {"__type__": "eece6Cx039Pc7RT/haaz88S", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "target": {"__id__": 2}, "fitFollowMode": 0, "fitMode": 3, "scale": 1, "offset": 0, "scale2": 1, "offset2": 0, "scale3": 1, "offset3": 0, "limit": false, "minLimit": false, "minLimitValue": 0, "maxLimit": false, "maxLimitValue": 0, "minLimit2": false, "minLimitValue2": 0, "maxLimit2": false, "maxLimitValue2": 0, "minLimit3": false, "minLimitValue3": 0, "maxLimit3": false, "maxLimitValue3": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "9a/m7wUUhI8YSA3G48oBNb", "sync": false}, {"__type__": "cc.Node", "_name": "SimpleLightSource", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 66}, {"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 80, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "a030aRZsCNCqJ1Ttsr9glNG", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "lightType": 1, "lightColor": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 228, "a": 255}, "lightIntensity": 1, "lightYScale": 0.8, "innerRadius": 300, "outerRadius": 800, "falloffExponent": 2, "_id": ""}, {"__type__": "eece6Cx039Pc7RT/haaz88S", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "target": {"__id__": 2}, "fitFollowMode": 0, "fitMode": 3, "scale": 1, "offset": 0, "scale2": 1, "offset2": 80, "scale3": 1, "offset3": 0, "limit": false, "minLimit": false, "minLimitValue": 0, "maxLimit": false, "maxLimitValue": 0, "minLimit2": false, "minLimitValue2": 0, "maxLimit2": false, "maxLimitValue2": 0, "minLimit3": false, "minLimitValue3": 0, "maxLimit3": false, "maxLimitValue3": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "39LtHqj/tHSK/xuntw3uNY", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7d232c36-074d-4772-a146-1b869118ed53"}, "fileId": "760KimqNVHxYG9nNusL00Z", "sync": false}]